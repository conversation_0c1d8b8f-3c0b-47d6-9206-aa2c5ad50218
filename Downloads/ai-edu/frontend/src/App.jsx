import React, { useState } from "react";
import ProfileCard from "./components/ProfileCard";
import UploadYouTube from "./components/UploadYouTube";
import ChatPane from "./components/ChatPane";

export default function App() {
  const [user] = useState({name:"Deep<PERSON>", email:"<EMAIL>"});
  const [lastUploaded, setLastUploaded] = useState(null);

  return (
    <div className="app">
      <div className="sidebar">
        <ProfileCard user={user} />
        <hr />
        <UploadYouTube onUploaded={(c)=>setLastUploaded(c)} />
        <div style={{padding:12}}>
          <h4>Recent Upload</h4>
          {lastUploaded ? <div>ID: {lastUploaded.id} URL: {lastUploaded.source_url}</div> : <div>No uploads yet</div>}
        </div>
      </div>
      <div className="main">
        <div className="header">
          <h2>AI Edu Chat 🚀</h2>
          <div>Welcome, {user.name}</div>
        </div>
        <ChatPane />
      </div>
    </div>
  );
}