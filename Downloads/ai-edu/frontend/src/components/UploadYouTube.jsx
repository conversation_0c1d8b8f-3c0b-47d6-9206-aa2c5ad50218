import React, { useState } from "react";
import { uploadYouTube, getContent } from "../services/api";

export default function UploadYouTube({ onUploaded }) {
  const [url, setUrl] = useState("");
  const [loading, setLoading] = useState(false);

  async function submit() {
    setLoading(true);
    try {
      const res = await uploadYouTube(url);
      // res will contain content id; poll for summary
      onUploaded(res);
      alert("Video queued for processing. It may take a minute.");
    } catch (err) {
      console.error(err);
      alert("Upload failed.");
    } finally {
      setLoading(false);
    }
  }

  return (
    <div style={{padding:12}}>
      <h3>Upload YouTube Link</h3>
      <input placeholder="https://youtube.com/..." value={url} onChange={(e)=>setUrl(e.target.value)} style={{width:"100%", padding:8, marginBottom:8}} />
      <button className="button" onClick={submit} disabled={loading}>{loading? "Uploading..." : "Upload & Summarize"}</button>
    </div>
  );
}