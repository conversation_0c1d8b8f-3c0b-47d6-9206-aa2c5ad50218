import React from "react";

export default function ProfileCard({ user }) {
  return (
    <div style={{padding:12}}>
      <div style={{display:"flex", alignItems:"center", gap:12}}>
        <div style={{width:56, height:56, borderRadius:28, background:"#2c7be5", color:"#fff", display:"flex", alignItems:"center", justifyContent:"center", fontWeight:600}}>
          {user?.name?.[0] || "U"}
        </div>
        <div>
          <div style={{fontWeight:700}}>{user?.name || "Guest User"}</div>
          <div style={{fontSize:12, color:"#666"}}>{user?.email || "Not logged in"}</div>
        </div>
      </div>
      <div style={{marginTop:16}}>
        <div style={{fontSize:12, color:"#888"}}>Preferences</div>
        <div style={{marginTop:8}}>
          <div style={{fontSize:13}}>Preferred format: <strong>Video</strong></div>
        </div>
      </div>
    </div>
  );
}