import React, { useState } from "react";
import { getContent } from "../services/api";

export default function ChatPane() {
  const [messages, setMessages] = useState([
    {from:"bot", text:"Hello! Ask me anything or paste a YouTube link to summarize a video."}
  ]);
  const [input, setInput] = useState("");

  async function sendMessage() {
    if(!input.trim()) return;
    const text = input.trim();
    setMessages(m=>[...m, {from:"user", text}]);
    setInput("");
    // If looks like YouTube link, hit upload endpoint via onUploaded from parent or assume another call
    if(text.includes("youtube.com") || text.includes("youtu.be")) {
      // naive flow: call upload endpoint directly
      try {
        const res = await fetch(`${import.meta.env.VITE_API_BASE || "http://localhost:8000"}/api/upload_youtube`, {
          method: "POST",
          headers: {"Content-Type":"application/json"},
          body: JSON.stringify({ youtube_url: text })
        });
        const data = await res.json();
        setMessages(m=>[...m, {from:"bot", text: "Video queued. I will summarize shortly. Content id: "+data.id}]);
      } catch (err) {
        setMessages(m=>[...m, {from:"bot", text: "Failed to queue video."}]);
      }
      return;
    }
    // normal question: for MVP we echo back or call a chat endpoint (not implemented in backend skeleton)
    setMessages(m=>[...m, {from:"bot", text:"(Chat backend not implemented in this MVP) I got your message: " + text } ]);
  }

  return (
    <div style={{display:"flex", flexDirection:"column", height:"100%"}}>
      <div className="chat-box" id="chatbox">
        {messages.map((m,i)=>(
          <div key={i} style={{marginBottom:12, textAlign: m.from==="user" ? "right":"left"}}>
            <div style={{display:"inline-block", background: m.from==="user" ? "#2c7be5":"#f1f3f6", color: m.from==="user" ? "#fff":"#111", padding:10, borderRadius:8}}>
              {m.text}
            </div>
          </div>
        ))}
      </div>

      <div className="footer">
        <input className="input" value={input} onChange={e=>setInput(e.target.value)} onKeyDown={(e)=>{ if(e.key === "Enter") sendMessage(); }} placeholder="Ask a question or paste YouTube link..." />
        <button className="button" onClick={sendMessage}>Send</button>
      </div>
    </div>
  );
}