body { margin:0; font-family: Inter, Arial, Helvetica, sans-serif; background:#f4f6fb; }
.app { display:flex; height:100vh; }
.sidebar { width: 280px; background:#fff; padding:16px; box-shadow: 2px 0 8px rgba(0,0,0,0.06); }
.main { flex:1; display:flex; flex-direction:column; padding:20px; }
.header { display:flex; justify-content:space-between; align-items:center; }
.chat-box { flex:1; margin-top:12px; overflow:auto; background:#fff; padding:16px; border-radius:8px; }
.footer { margin-top:12px; display:flex; gap:8px; }
.input { flex:1; padding:8px 12px; border-radius:6px; border:1px solid #ddd; }
.button { padding:10px 12px; border-radius:6px; background:#2c7be5; color:white; border:none; cursor:pointer; }