import os
from pydantic_settings import BaseSettings

class Settings(BaseSettings):
    DATABASE_URL: str = os.getenv("DATABASE_URL", "**************************************/ai_edu")
    REDIS_URL: str = os.getenv("REDIS_URL", "redis://redis:6379/0")
    YOUTUBE_API_KEY: str = os.getenv("YOUTUBE_API_KEY", "")
    GEMINI_API_KEY: str = os.getenv("GEMINI_API_KEY", "")
    SENT_TRANS_MODEL: str = os.getenv("SENT_TRANS_MODEL", "sentence-transformers/all-MiniLM-L6-v2")
    HF_SUMMARIZER_MODEL: str = os.getenv("HF_SUMMARIZER_MODEL", "facebook/bart-large-cnn")
    FAISS_INDEX_PATH: str = os.getenv("FAISS_INDEX_PATH", "/data/faiss.index")
    CHUNK_SIZE_SECONDS: int = 60
    class Config:
        env_file = "../.env"

settings = Settings()