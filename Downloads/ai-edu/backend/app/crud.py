from sqlalchemy.orm import Session
from . import models, schemas
from typing import Optional

def create_content(db: Session, title: str, ctype: str, source_url: Optional[str]=None, transcript: Optional[str]=None, summary: Optional[str]=None, metadata: dict={}):
    obj = models.Content(title=title, type=ctype, source_url=source_url, transcript=transcript, summary=summary, metadata=metadata)
    db.add(obj); db.commit(); db.refresh(obj)
    return obj

def get_content(db: Session, content_id: int):
    return db.query(models.Content).filter(models.Content.id == content_id).first()