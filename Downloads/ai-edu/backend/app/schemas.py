from pydantic import BaseModel
from typing import Optional, List, Dict

class UserCreate(BaseModel):
    email: str
    name: Optional[str]
    password: str

class UserOut(BaseModel):
    id: int
    email: str
    name: Optional[str]
    class Config:
        orm_mode = True

class UploadYouTubeIn(BaseModel):
    youtube_url: str

class ContentOut(BaseModel):
    id: int
    title: Optional[str]
    type: Optional[str]
    source_url: Optional[str]
    summary: Optional[str]
    class Config:
        orm_mode = True