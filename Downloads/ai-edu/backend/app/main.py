from fastapi import <PERSON><PERSON><PERSON>, Depends, HTTPException
from .database import engine, Base, get_db
from . import models, crud, schemas
from sqlalchemy.orm import Session
from fastapi.middleware.cors import CORSMiddleware
from .workers import process_youtube_and_index

Base.metadata.create_all(bind=engine)

app = FastAPI(title="AI Edu Backend")

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # change in prod
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.post("/api/upload_youtube", response_model=schemas.ContentOut)
def upload_youtube(payload: schemas.UploadYouTubeIn, db: Session = Depends(get_db)):
    # create content entry
    c = crud.create_content(db, title="YouTube Upload", ctype="video", source_url=payload.youtube_url)
    # spawn worker
    process_youtube_and_index.delay(payload.youtube_url, c.id)
    return c

@app.get("/api/content/{content_id}", response_model=schemas.ContentOut)
def get_content(content_id: int, db: Session = Depends(get_db)):
    c = crud.get_content(db, content_id)
    if not c:
        raise HTTPException(status_code=404, detail="Content not found")
    return c