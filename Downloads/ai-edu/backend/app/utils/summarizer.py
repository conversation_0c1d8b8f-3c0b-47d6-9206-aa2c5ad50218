# Summarizer wrapper: Gemini API primary, HF fallback
import requests
from .config import settings
from transformers import pipeline

# instantiate HF summarizer lazily
hf_summarizer = None

def hf_summarize(text: str, max_length=200):
    global hf_summarizer
    if hf_summarizer is None:
        hf_summarizer = pipeline("summarization", model=settings.HF_SUMMARIZER_MODEL, truncation=True)
    # chunk if needed
    chunks = [text[i:i+1500] for i in range(0, len(text), 1500)]
    summaries = [hf_summarizer(c, max_length=max_length, min_length=30)[0]['summary_text'] for c in chunks]
    return "\n".join(summaries)

def gemini_summarize(text: str, summary_style: str="bullet_points", max_tokens: int = 300):
    """
    Placeholder wrapper. Replace endpoint & auth with the correct Gemini endpoint and auth.
    This is a generic HTTP request to a hypothetical Gemini REST endpoint for text generation.
    """
    if not settings.GEMINI_API_KEY:
        # fallback to HF summarizer
        return hf_summarize(text)
    endpoint = "https://gemini.api.example.com/v1/generate"  # <--- replace with real Gemini endpoint
    prompt = f"Summarize the following in {summary_style} style. Provide clear bullet points and a 1-sentence takeaway.\n\n{text}"
    headers = {"Authorization": f"Bearer {settings.GEMINI_API_KEY}", "Content-Type": "application/json"}
    payload = {"prompt": prompt, "max_tokens": max_tokens}
    r = requests.post(endpoint, json=payload, headers=headers, timeout=60)
    if r.status_code == 200:
        return r.json().get("text") or r.json().get("output") or hf_summarize(text)
    else:
        return hf_summarize(text)