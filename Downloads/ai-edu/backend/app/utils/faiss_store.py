import faiss
import numpy as np
import os
from ..config import settings

class FaissStore:
    def __init__(self, dim: int, index_path: str = None):
        self.dim = dim
        self.index_path = index_path or settings.FAISS_INDEX_PATH
        if os.path.exists(self.index_path):
            self.index = faiss.read_index(self.index_path)
        else:
            # index with IVF+PQ could be used; for simplicity use IndexFlatL2
            self.index = faiss.IndexFlatL2(dim)
        # metadata map: id -> metadata
        self.meta = {}
        self.next_id = 0

    def add(self, vectors: np.ndarray, metas: list):
        n = vectors.shape[0]
        self.index.add(vectors)
        start_id = self.next_id
        for i, m in enumerate(metas):
            self.meta[start_id + i] = m
        self.next_id += n
        self.save()

    def search(self, query_vec: np.ndarray, k=5):
        D, I = self.index.search(query_vec, k)
        results = []
        for ids, dists in zip(I, D):
            for idx, dist in zip(ids, dists):
                if idx == -1:
                    continue
                results.append({"id": int(idx), "dist": float(dist), "meta": self.meta.get(int(idx))})
        return results

    def save(self):
        os.makedirs(os.path.dirname(self.index_path) or ".", exist_ok=True)
        faiss.write_index(self.index, self.index_path)
        # Save metadata to a JSON or pickled file next to index
        import json
        with open(self.index_path + ".meta.json", "w", encoding="utf-8") as f:
            json.dump(self.meta, f, ensure_ascii=False, indent=2)