from sentence_transformers import SentenceTransformer
import numpy as np
from .config import settings

_model = None

def get_model():
    global _model
    if _model is None:
        _model = SentenceTransformer(settings.SENT_TRANS_MODEL)
    return _model

def embed_texts(texts: list):
    model = get_model()
    embeddings = model.encode(texts, show_progress_bar=False, convert_to_numpy=True)
    return embeddings.astype(np.float32)