# Utilities to fetch YouTube transcripts and metadata
from youtube_transcript_api import YouTubeTranscriptApi, TranscriptsDisabled, NoTranscriptFound
import re
import requests
from urllib.parse import urlparse, parse_qs
from .config import settings

def extract_video_id(youtube_url: str) -> str:
    # supports many youtube url formats
    parsed = urlparse(youtube_url)
    if parsed.hostname in ("www.youtube.com","youtube.com"):
        qs = parse_qs(parsed.query)
        return qs.get("v", [None])[0]
    if parsed.hostname == "youtu.be":
        return parsed.path.lstrip("/")
    # fallback pattern
    m = re.search(r"v=([A-Za-z0-9_\-]+)", youtube_url)
    return m.group(1) if m else None

def fetch_transcript(video_id: str) -> str:
    try:
        transcript_list = YouTubeTranscriptApi.get_transcript(video_id, languages=['en'])
        # join text
        text = " ".join([seg["text"] for seg in transcript_list])
        return text
    except (TranscriptsDisabled, NoTranscriptFound):
        return None

def fetch_video_title(video_id: str) -> str:
    # Use YouTube Data API as fallback if you need title
    if not settings.YOUTUBE_API_KEY:
        return None
    url = f"https://www.googleapis.com/youtube/v3/videos?part=snippet&id={video_id}&key={settings.YOUTUBE_API_KEY}"
    r = requests.get(url)
    if r.status_code != 200:
        return None
    items = r.json().get("items",[])
    if not items:
        return None
    return items[0]["snippet"]["title"]