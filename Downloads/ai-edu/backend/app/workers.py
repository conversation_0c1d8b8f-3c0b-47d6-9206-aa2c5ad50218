# Celery worker for heavy tasks
from celery import Celery
from .config import settings
from .utils.youtube_utils import extract_video_id, fetch_transcript, fetch_video_title
from .utils.summarizer import gemini_summarize
from .utils.embeddings import embed_texts
from .utils.faiss_store import FaissStore
from sqlalchemy.orm import Session
from .database import SessionLocal
from . import crud, models
import numpy as np

celery = Celery("workers", broker=settings.REDIS_URL, backend=settings.REDIS_URL)

@celery.task()
def process_youtube_and_index(youtube_url: str, content_id: int):
    db: Session = SessionLocal()
    try:
        vid = extract_video_id(youtube_url)
        transcript = None
        if vid:
            transcript = fetch_transcript(vid)
            title = fetch_video_title(vid) or ("YouTube Video " + vid)
        else:
            title = "YouTube Video"
        if transcript is None:
            # fallback: TODO: download audio + run ASR
            transcript = "Transcript not available from captions. Consider downloading audio and running ASR."
        # Summarize (Gemini primary)
        summary = gemini_summarize(transcript)
        # store to DB
        content = db.query(models.Content).get(content_id)
        content.transcript = transcript
        content.summary = summary
        db.add(content); db.commit()
        # chunk transcript into segments for embeddings
        seg_texts = []
        seg_metas = []
        words = transcript.split()
        chunk_size = 150  # words per chunk
        for i in range(0, len(words), chunk_size):
            chunk = " ".join(words[i:i+chunk_size])
            if chunk.strip():
                seg_texts.append(chunk)
                seg_metas.append({"content_id": content_id, "start_word": i, "end_word": i+chunk_size})
        # embed and add to FAISS
        embeddings = embed_texts(seg_texts)
        dim = embeddings.shape[1]
        store = FaissStore(dim)
        store.add(embeddings, seg_metas)
        return {"status": "ok", "content_id": content_id}
    finally:
        db.close()