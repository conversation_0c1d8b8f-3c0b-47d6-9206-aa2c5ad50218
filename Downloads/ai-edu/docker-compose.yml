version: "3.8"
services:
  db:
    image: postgres:15
    environment:
      POSTGRES_PASSWORD: password
      POSTGRES_USER: postgres
      POSTGRES_DB: ai_edu
    volumes:
      - pgdata:/var/lib/postgresql/data
    ports:
      - "5432:5432"

  redis:
    image: redis:7
    ports:
      - "6379:6379"

  backend:
    build: ./backend
    depends_on:
      - db
      - redis
    environment:
      - DATABASE_URL=**************************************/ai_edu
      - REDIS_URL=redis://redis:6379/0
      - YOUTUBE_API_KEY=${YOUTUBE_API_KEY}
      - GEMINI_API_KEY=${GEMINI_API_KEY}
    ports:
      - "8000:8000"
    volumes:
      - ./backend:/app

  worker:
    build: ./backend
    command: celery -A app.workers.celery worker --loglevel=info
    depends_on:
      - backend
      - redis
    environment:
      - DATABASE_URL=**************************************/ai_edu
      - REDIS_URL=redis://redis:6379/0
      - YOUTUBE_API_KEY=${YOUTUBE_API_KEY}
      - GEMINI_API_KEY=${GEMINI_API_KEY}
    volumes:
      - ./backend:/app

  frontend:
    build: ./frontend
    command: ["npm","run","dev"]
    ports:
      - "3000:5173"
    depends_on:
      - backend
    environment:
      - VITE_API_BASE=http://localhost:8000

volumes:
  pgdata: